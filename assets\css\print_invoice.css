
/* إخفاء بطاقة معلومات الفاتورة عند الطباعة */
#invoiceHeaderInfoCard {
    display: none !important;
}

#invoiceItemActionsHeader,
.invoice-item-actions-cell,
#invoiceItemActionsFooter {
    display: none !important;
}

body {
    font-family: 'Arial', sans-serif; /* خط مناسب للطباعة */
    margin: 20px;
    color: #000; /* استخدام اللون الأسود للنص */
}

/* إخفاء شريط التنقل والفوتر والأزرار وما شابه */
.navbar,
.footer-area, /* افترض أن لديك فوتر بهذا الكلاس */
.card-header div a.btn, /* أزرار الإجراءات في رأس بطاقة تفاصيل الفاتورة */
.card-footer, /* تذييل البطاقة الذي يحتوي على زر الرجوع */
.container > .d-flex.justify-content-between, /* عنوان الصفحة وزر إضافة عميل */
.container > .card.mb-4.shadow-sm, /* بطاقة البحث إذا كانت موجودة في view_invoice */
.card.shadow-lg.mt-4, /* بطاقة إضافة منتج للفاتورة */
form button[name="delete_item"], /* أزرار حذف البنود */
script /* إخفاء أي عناصر script */
 {
    display: none !important;
}

.card, .card-body, .card-header {
    border: 1px solid #ccc !important; /* إظهار حدود خفيفة للبطاقات */
    box-shadow: none !important; /* إزالة الظلال */
    padding: 10px !important; /* تقليل الحشو للطباعة */
}

.card-header {
    background-color: #f8f9fa !important; /* خلفية فاتحة لرؤوس البطاقات */
    color: #000 !important;
}
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}
.table th, .table td {
    border: 1px solid #dee2e6;
    padding: 0.5rem;
    text-align: right; /* محاذاة لليمين للغة العربية */
}
.table thead.table-dark th {
    background-color: #e9ecef !important; /* خلفية أفتح لرأس الجدول */
    color: #000 !important;
    border-bottom-width: 2px;
}
h1, h3, h4 {
    color: #000;
}
/* يمكنك إضافة المزيد من التنسيقات لجعل الفاتورة تبدو احترافية في الطباعة */